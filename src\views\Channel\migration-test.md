# 渠道自服务模块迁移测试指南

## 🧪 测试环境准备

### 1. 启动开发服务器
```bash
npm run dev
```

### 2. 登录系统
- 访问: http://localhost:4000/
- 使用测试账号登录
- 确保用户有渠道自服务相关权限

## 📋 功能测试清单

### ✅ 已迁移组件测试

#### 1. 充值管理模块测试
**访问路径**: `/newcmi/channel/deposit`

**测试项目**:
- [ ] 页面正常加载
- [ ] 账户信息显示正确
- [ ] 合作模式、渠道模式、币种显示
- [ ] 营销账户、信用账户金额显示
- [ ] "可购套餐"按钮功能
- [ ] "流量详情"按钮功能  
- [ ] "充值"按钮功能
- [ ] "营销账户详情"按钮跳转

**营销账户详情页测试**:
**访问路径**: `/newcmi/channel/deposit/marketing-account`
- [ ] 页面正常加载
- [ ] 营销账户流水按钮功能
- [ ] 数据表格显示
- [ ] 分页功能
- [ ] 返回按钮功能
- [ ] 流水弹窗功能
- [ ] 导出功能

#### 2. 库存管理模块测试
**访问路径**: `/newcmi/channel/stock`

**测试项目**:
- [ ] 页面正常加载
- [ ] 搜索表单功能
- [ ] 订单号搜索
- [ ] 时间段筛选
- [ ] 搜索按钮功能
- [ ] "显示ICCID"按钮功能
- [ ] 数据表格显示
- [ ] 任务状态显示正确
- [ ] 详情按钮功能
- [ ] 分页功能

#### 3. 订单管理模块测试
**访问路径**: `/newcmi/channel/order`

**测试项目**:
- [ ] 页面正常加载
- [ ] 搜索表单功能
- [ ] 套餐名称搜索
- [ ] 号码搜索
- [ ] 时间段筛选
- [ ] 归属渠道筛选
- [ ] "月账单"按钮功能
- [ ] 搜索按钮功能
- [ ] 导出按钮功能
- [ ] "流量详情"按钮功能
- [ ] 数据表格显示
- [ ] 订单类型标签显示
- [ ] 订单状态标签显示
- [ ] 退订按钮功能
- [ ] 分页功能

#### 4. 流量池管理模块测试
**访问路径**: `/newcmi/channel/flowpool`

**测试项目**:
- [ ] 页面正常加载
- [ ] 渠道信息显示
- [ ] 搜索表单功能
- [ ] 流量池名称搜索
- [ ] 使用状态筛选
- [ ] 上架状态筛选
- [ ] 搜索按钮功能
- [ ] 导出按钮功能
- [ ] 数据表格显示
- [ ] 流量数据显示正确
- [ ] 状态标签显示正确
- [ ] "显示ICCID"按钮功能
- [ ] "阈值提醒"按钮功能
- [ ] 阈值设置弹窗功能
- [ ] 分页功能

#### 5. AQ码管理模块测试
**访问路径**: `/newcmi/channel/aqcode`

**测试项目**:
- [ ] 页面正常加载
- [ ] 搜索表单功能
- [ ] AQ码搜索
- [ ] 状态筛选
- [ ] 时间段筛选
- [ ] 搜索按钮功能
- [ ] "生成AQ码"按钮功能
- [ ] 导出按钮功能
- [ ] 数据表格显示
- [ ] AQ码状态显示正确
- [ ] 禁用按钮功能
- [ ] 生成AQ码弹窗功能
- [ ] 分页功能

## 🐛 常见问题排查

### 1. 页面404错误
**可能原因**:
- 路由配置未生效
- 用户权限不足
- 组件文件路径错误

**解决方案**:
```bash
# 检查路由配置
cat src/types/cmi-menu.ts | grep -A 10 "channel_mngr"

# 检查组件文件是否存在
ls -la src/views/CMI/Channel/
```

### 2. 组件加载失败
**可能原因**:
- 组件导入路径错误
- 依赖组件缺失
- TypeScript 类型错误

**解决方案**:
```bash
# 检查控制台错误信息
# 检查组件导入路径
# 安装缺失的依赖
```

### 3. 权限验证失败
**可能原因**:
- 用户权限配置不正确
- 权限检查函数未实现

**解决方案**:
```typescript
// 临时禁用权限检查进行测试
const hasPermission = (permission: string): boolean => {
  return true // 临时返回 true
}
```

## 📊 测试结果记录

### 测试环境信息
- **测试时间**: ___________
- **测试人员**: ___________
- **浏览器版本**: ___________
- **Node.js 版本**: ___________

### 测试结果统计
- **总测试项**: 约 60 项
- **通过项**: _____ 项
- **失败项**: _____ 项
- **通过率**: _____%

### 发现的问题
1. ________________________________
2. ________________________________
3. ________________________________

### 改进建议
1. ________________________________
2. ________________________________
3. ________________________________

## 🚀 下一步计划

1. **继续迁移剩余组件** (19个)
2. **完善API接口适配**
3. **优化用户体验**
4. **编写单元测试**
5. **性能优化**
