# API 接口层设计详解 - Vue 3 + TypeScript 学习文档

## 概述

本文档以 `src/api/cmi/auth.ts` 为例，详细解析 Vue 3 项目中 API 接口层的设计模式、TypeScript 类型定义和最佳实践。

## 文件结构

```
src/api/
├── cmi/
│   └── auth.ts              # CMI 认证相关接口
├── types/                   # 类型定义
└── index.ts                # 统一导出
```

## TypeScript 类型系统

### 1. 接口类型定义

```typescript
// 登录请求数据类型
export interface LoginData {
  username: string          // 用户名
  password: string          // 密码
  captcha?: string          // 验证码（可选）
  captchaId?: string        // 验证码ID（可选）
  smsCode?: string          // 短信验证码（可选）
  type?: number             // 登录类型（可选）
}

// 登录响应数据类型
export interface LoginResponse {
  oauth2AccessToken: {
    access_token: string
  }
  userDetails: {
    id: string
    username: string
    roleId: string
    rePassword: number      // 密码是否需要更新
    pagePrivileges: Array<{
      access: string
      url: string
      buttons: string[]
    }>
    needVerifyCode?: string // 是否需要验证码
  }
}
```

**TypeScript 优势**：
- 编译时类型检查
- 智能代码提示
- 重构安全性
- 文档化的代码

### 2. 泛型约束

```typescript
// 使用泛型定义通用响应类型
interface IResponse<T = any> {
  code: string
  data: T
  msg?: string
}

// 接口函数使用泛型
export const login = (data: LoginData) => {
  return request.post<IResponse<LoginResponse>>({
    url: '/auth/login',
    data
  })
}
```

**泛型的作用**：
- 类型复用
- 保持类型安全
- 提供灵活性

### 3. 可选属性和联合类型

```typescript
// 可选属性
interface UserInfo {
  id: string
  name: string
  email?: string        // 可选属性
  avatar?: string       // 可选属性
}

// 联合类型
type LoginType = 'password' | 'sms' | 'sso'

// 字面量类型
type ResponseCode = '0000' | '4001' | '4002'
```

## 接口设计模式

### 1. RESTful API 设计

```typescript
// 用户相关接口
export const getUserInfo = () => {
  return request.get<IResponse<UserInfo>>({
    url: '/sys/api/v1/user/getUserInfo'
  })
}

export const updateUserInfo = (data: Partial<UserInfo>) => {
  return request.put<IResponse<UserInfo>>({
    url: '/sys/api/v1/user/updateUserInfo',
    data
  })
}

export const deleteUser = (id: string) => {
  return request.delete<IResponse<void>>({
    url: `/sys/api/v1/user/${id}`
  })
}
```

**RESTful 原则**：
- GET: 获取资源
- POST: 创建资源
- PUT: 更新资源
- DELETE: 删除资源

### 2. 统一的错误处理

```typescript
// 在 axios 配置中统一处理
const responseInterceptor = (response: AxiosResponse) => {
  const result = response.data
  
  // CMI 项目特殊错误码处理
  if (result.code === '4001' || result.code === '4002') {
    // token校验失败，跳转到登录页面
    userStore.logout()
    router.push({ name: 'login' })
    setTimeout(() => {
      ElMessage.error('Token已过期，请重新登录')
    }, 1000)
    return Promise.reject(new Error('Token已过期'))
  }
  
  return response
}
```

### 3. 请求拦截器

```typescript
const requestInterceptor = (config: InternalAxiosRequestConfig) => {
  const userStore = useUserStoreWithOut()
  
  // CMI 项目特殊处理：添加 token 和用户名到请求头
  if (userStore.getToken) {
    const token = userStore.getToken
    const userName = userStore.getUserName || ''
    
    // 添加 Authorization header
    if (config.params !== false) {
      config.headers.Authorization = `bearer ${token}`
    }
    
    // 添加用户名到请求头（CMI项目需求）
    if (userName) {
      config.headers.userName = encodeURIComponent(userName)
    }
  }
  
  return config
}
```

## 具体接口实现

### 1. 认证相关接口

```typescript
// 普通登录
export const login = (data: LoginData) => {
  return request.post<IResponse<LoginResponse>>({
    url: '/auth/login',
    data
  })
}

// SSO 登录
export const ssoLoginGDS = (data: SSOLoginData) => {
  return request.post<IResponse<LoginResponse>>({
    url: '/aep/sso/ssoLoginGDS',
    data
  })
}

// 登出
export const logout = () => {
  return request.post<IResponse<any>>({
    url: 'logout'
  })
}

// 清除 Redis 登出
export const logoutClearRedis = (userName: string) => {
  return request.delete<IResponse<any>>({
    url: `/auth/logout?userName=${userName}`
  })
}
```

### 2. 验证码相关接口

```typescript
// 验证码响应类型
export interface CaptchaResponse {
  captchaImage: string
  captchaId: string
}

// 获取图形验证码
export const getVerCode = () => {
  return request.get<IResponse<CaptchaResponse>>({
    url: '/auth/code/getVerCode'
  })
}

// 获取验证码开关配置
export const getConfigure = (data: any) => {
  return request.post<IResponse<any>>({
    url: '/auth/code/getIsOpen',
    data
  })
}
```

### 3. 用户信息接口

```typescript
// 用户信息类型
export interface UserInfo {
  id: string
  username: string
  email?: string
  phone?: string
  avatar?: string
  roles: string[]
  permissions: string[]
}

// 获取用户信息
export const getUserInfo = () => {
  return request.get<IResponse<UserInfo>>({
    url: '/sys/api/v1/user/getUserInfo'
  })
}

// 获取菜单列表
export interface MenuData {
  id: string
  name: string
  path: string
  icon?: string
  children?: MenuData[]
}

export const getMenuList = () => {
  return request.get<IResponse<MenuData[]>>({
    url: '/sys/api/v1/user/getMenuList'
  })
}
```

## 高级特性

### 1. 请求取消

```typescript
import { CancelToken } from 'axios'

// 创建取消令牌
const source = CancelToken.source()

export const searchUsers = (params: SearchParams) => {
  return request.get<IResponse<UserInfo[]>>({
    url: '/api/users/search',
    params,
    cancelToken: source.token  // 添加取消令牌
  })
}

// 取消请求
source.cancel('Operation canceled by the user.')
```

### 2. 请求重试

```typescript
// 在 axios 配置中添加重试逻辑
const retryRequest = (error: AxiosError) => {
  const config = error.config as any
  
  if (!config || !config.retry) return Promise.reject(error)
  
  config.__retryCount = config.__retryCount || 0
  
  if (config.__retryCount >= config.retry) {
    return Promise.reject(error)
  }
  
  config.__retryCount += 1
  
  const backoff = new Promise(resolve => {
    setTimeout(() => {
      resolve(void 0)
    }, config.retryDelay || 1000)
  })
  
  return backoff.then(() => {
    return axios(config)
  })
}
```

### 3. 缓存机制

```typescript
// 简单的内存缓存
const cache = new Map<string, { data: any; timestamp: number }>()

export const getCachedData = async (key: string, fetcher: () => Promise<any>, ttl = 5000) => {
  const cached = cache.get(key)
  
  if (cached && Date.now() - cached.timestamp < ttl) {
    return cached.data
  }
  
  const data = await fetcher()
  cache.set(key, { data, timestamp: Date.now() })
  
  return data
}

// 使用缓存
export const getUserInfoCached = () => {
  return getCachedData('userInfo', getUserInfo)
}
```

## 错误处理策略

### 1. 分层错误处理

```typescript
// API 层：基础错误处理
export const login = async (data: LoginData) => {
  try {
    const response = await request.post('/auth/login', data)
    return response
  } catch (error) {
    // 记录错误日志
    console.error('Login API Error:', error)
    throw error  // 重新抛出，让上层处理
  }
}

// Store 层：业务错误处理
async cmiLogin(loginData: LoginData) {
  try {
    const res = await login(loginData)
    // 处理成功逻辑
    return res
  } catch (error) {
    // 业务层错误处理
    ElMessage.error('登录失败，请检查用户名和密码')
    throw error
  }
}

// 组件层：用户交互处理
const handleLogin = async () => {
  try {
    await userStore.cmiLogin(formData)
    // 成功后的 UI 处理
  } catch (error) {
    // UI 层错误处理
    loading.value = false
  }
}
```

### 2. 错误类型定义

```typescript
// 自定义错误类型
export class APIError extends Error {
  constructor(
    public code: string,
    public message: string,
    public data?: any
  ) {
    super(message)
    this.name = 'APIError'
  }
}

// 错误处理函数
export const handleAPIError = (error: any): APIError => {
  if (error.response) {
    // 服务器响应错误
    return new APIError(
      error.response.data.code || 'SERVER_ERROR',
      error.response.data.message || '服务器错误',
      error.response.data
    )
  } else if (error.request) {
    // 网络错误
    return new APIError('NETWORK_ERROR', '网络连接失败')
  } else {
    // 其他错误
    return new APIError('UNKNOWN_ERROR', error.message || '未知错误')
  }
}
```

## 测试策略

### 1. 单元测试

```typescript
import { vi, describe, it, expect } from 'vitest'
import { login } from '@/api/cmi/auth'

// Mock axios
vi.mock('@/axios', () => ({
  default: {
    post: vi.fn()
  }
}))

describe('Auth API', () => {
  it('should login successfully', async () => {
    const mockResponse = {
      code: '0000',
      data: {
        oauth2AccessToken: { access_token: 'test-token' },
        userDetails: { id: '1', username: 'test' }
      }
    }
    
    vi.mocked(request.post).mockResolvedValue(mockResponse)
    
    const result = await login({ username: 'test', password: 'test' })
    
    expect(result).toEqual(mockResponse)
  })
})
```

### 2. 集成测试

```typescript
// 使用 MSW (Mock Service Worker) 进行集成测试
import { rest } from 'msw'
import { setupServer } from 'msw/node'

const server = setupServer(
  rest.post('/auth/login', (req, res, ctx) => {
    return res(
      ctx.json({
        code: '0000',
        data: {
          oauth2AccessToken: { access_token: 'test-token' }
        }
      })
    )
  })
)

beforeAll(() => server.listen())
afterEach(() => server.resetHandlers())
afterAll(() => server.close())
```

## 最佳实践总结

### 1. 类型安全
- 为所有接口定义明确的类型
- 使用泛型提高代码复用性
- 避免使用 `any` 类型

### 2. 错误处理
- 分层处理错误
- 提供有意义的错误信息
- 统一错误处理机制

### 3. 代码组织
- 按功能模块组织接口
- 统一的命名规范
- 清晰的文档注释

### 4. 性能优化
- 合理使用缓存
- 请求去重
- 适当的重试机制

这个 API 接口层设计展示了 Vue 3 + TypeScript 项目中现代化的接口管理方式，为大型应用提供了类型安全、可维护的接口解决方案。
