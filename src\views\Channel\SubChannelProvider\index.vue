<template>
  <!-- 子渠道提供商管理 -->
  <ContentWrap>
    <el-card>
      <div class="search-container">
        <el-form :model="searchForm" inline>
          <el-form-item label="提供商名称：">
            <el-input
              v-model="searchForm.providerName"
              placeholder="请输入提供商名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="提供商类型：">
            <el-select
              v-model="searchForm.providerType"
              placeholder="请选择类型"
              clearable
              style="width: 150px"
            >
              <el-option :value="1" label="一级代理" />
              <el-option :value="2" label="二级代理" />
              <el-option :value="3" label="直接客户" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态：">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择状态"
              clearable
              style="width: 150px"
            >
              <el-option :value="1" label="正常" />
              <el-option :value="2" label="暂停" />
              <el-option :value="0" label="停用" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              v-if="hasPermission('search')"
              type="primary"
              :loading="searchLoading"
              @click="handleSearch"
            >
              <Icon icon="ep:search" class="mr-5px" />
              搜索
            </el-button>
            <el-button v-if="hasPermission('add')" type="success" @click="handleAdd">
              <Icon icon="ep:plus" class="mr-5px" />
              新增提供商
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格 -->
      <div style="margin-top: 20px">
        <el-table :data="tableData" v-loading="loading" border>
          <el-table-column prop="providerCode" label="提供商编码" min-width="150" />
          <el-table-column prop="providerName" label="提供商名称" min-width="200" />
          <el-table-column prop="providerType" label="提供商类型" min-width="120" align="center">
            <template #default="{ row }">
              <el-tag :type="getProviderTypeTag(row?.providerType)" v-if="row">
                {{ getProviderTypeText(row?.providerType) }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="contactPerson" label="联系人" min-width="100" />
          <el-table-column prop="contactPhone" label="联系电话" min-width="130" />
          <el-table-column prop="contactEmail" label="联系邮箱" min-width="180" />
          <el-table-column prop="cooperationStartDate" label="合作开始日期" min-width="130" />
          <el-table-column prop="totalAmount" label="合作金额" min-width="120" align="right">
            <template #default="{ row }">
              {{ row ? (row.totalAmount || 0).toFixed(2) : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="usedAmount" label="已使用金额" min-width="120" align="right">
            <template #default="{ row }">
              {{ row ? (row.usedAmount || 0).toFixed(2) : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="remainingAmount" label="剩余金额" min-width="120" align="right">
            <template #default="{ row }">
              <span :style="{ color: getRemainingAmountColor(row?.remainingAmount) }">
                {{ row ? (row.remainingAmount || 0).toFixed(2) : '-' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" min-width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getStatusTag(row?.status)" v-if="row">
                {{ getStatusText(row?.status) }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="200" align="center" fixed="right">
            <template #default="{ row }">
              <div v-if="row" class="action-buttons">
                <el-button
                  v-if="hasPermission('view')"
                  type="primary"
                  size="small"
                  @click="handleView(row)"
                >
                  查看
                </el-button>
                <el-button
                  v-if="hasPermission('edit')"
                  type="warning"
                  size="small"
                  @click="handleEdit(row)"
                >
                  编辑
                </el-button>
                <el-button
                  v-if="hasPermission('recharge')"
                  type="success"
                  size="small"
                  @click="handleRecharge(row)"
                >
                  充值
                </el-button>
                <el-button
                  v-if="hasPermission('freeze') && row.status === 1"
                  type="warning"
                  size="small"
                  @click="handleFreeze(row)"
                >
                  暂停
                </el-button>
                <el-button
                  v-if="hasPermission('unfreeze') && row.status === 2"
                  type="success"
                  size="small"
                  @click="handleUnfreeze(row)"
                >
                  恢复
                </el-button>
                <el-button
                  v-if="hasPermission('delete') && row.status === 0"
                  type="danger"
                  size="small"
                  @click="handleDelete(row)"
                >
                  删除
                </el-button>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div style="margin-top: 20px; text-align: right">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'

// 权限检查函数
const hasPermission = (permission: string): boolean => {
  console.log(`🔍 [子渠道提供商权限检查] ${permission}: 允许访问`)
  return true
}

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)

const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

const searchForm = reactive({
  providerName: '',
  providerType: null as number | null,
  status: null as number | null
})

const tableData = ref<any[]>([])

// 方法
const getProviderTypeTag = (
  type: number
): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<number, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    1: 'primary',
    2: 'success',
    3: 'warning'
  }
  return typeMap[type] || 'info'
}

const getProviderTypeText = (type: number) => {
  const typeMap: Record<number, string> = {
    1: '一级代理',
    2: '二级代理',
    3: '直接客户'
  }
  return typeMap[type] || '未知'
}

const getStatusTag = (status: number): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const statusMap: Record<number, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    1: 'success',
    2: 'warning',
    0: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    1: '正常',
    2: '暂停',
    0: '停用'
  }
  return statusMap[status] || '未知'
}

const getRemainingAmountColor = (amount: number) => {
  if (!amount) return '#999'
  if (amount < 1000) return '#f56c6c'
  if (amount < 5000) return '#e6a23c'
  return '#67c23a'
}

const handleSearch = () => {
  currentPage.value = 1
  getTableData()
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  getTableData()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  getTableData()
}

const handleAdd = () => {
  ElMessage.info('新增提供商功能开发中...')
}

const handleView = (row: any) => {
  ElMessage.info(`查看提供商详情: ${row.providerName}`)
}

const handleEdit = (row: any) => {
  ElMessage.info(`编辑提供商: ${row.providerName}`)
}

const handleRecharge = (row: any) => {
  ElMessage.info(`提供商充值: ${row.providerName}`)
}

const handleFreeze = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要暂停提供商 "${row.providerName}" 吗？`, '确认暂停', {
      type: 'warning'
    })

    ElMessage.success('暂停成功')
    getTableData()
  } catch (error) {
    // 用户取消操作
  }
}

const handleUnfreeze = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要恢复提供商 "${row.providerName}" 吗？`, '确认恢复', {
      type: 'warning'
    })

    ElMessage.success('恢复成功')
    getTableData()
  } catch (error) {
    // 用户取消操作
  }
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除提供商 "${row.providerName}" 吗？`, '确认删除', {
      type: 'warning'
    })

    ElMessage.success('删除成功')
    getTableData()
  } catch (error) {
    // 用户取消操作
  }
}

// 获取表格数据
const getTableData = async () => {
  try {
    loading.value = true
    // TODO: 实现API调用
    // 模拟数据
    tableData.value = [
      {
        id: 1,
        providerCode: 'SUB001',
        providerName: '子渠道提供商A',
        providerType: 1,
        contactPerson: '张三',
        contactPhone: '13800138000',
        contactEmail: '<EMAIL>',
        cooperationStartDate: '2024-01-01',
        totalAmount: 100000.0,
        usedAmount: 65000.0,
        remainingAmount: 35000.0,
        status: 1
      },
      {
        id: 2,
        providerCode: 'SUB002',
        providerName: '子渠道提供商B',
        providerType: 2,
        contactPerson: '李四',
        contactPhone: '13900139000',
        contactEmail: '<EMAIL>',
        cooperationStartDate: '2024-01-15',
        totalAmount: 50000.0,
        usedAmount: 48000.0,
        remainingAmount: 2000.0,
        status: 2
      }
    ]
    total.value = 2
  } catch (error) {
    ElMessage.error('获取子渠道提供商数据失败')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  getTableData()
})
</script>

<style scoped>
.search-container {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  justify-content: center;
}

.action-buttons .el-button {
  margin: 2px;
}
</style>
