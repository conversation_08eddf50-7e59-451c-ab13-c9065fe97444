<template>
  <!-- 燃料包管理 -->
  <ContentWrap>
    <el-card>
      <div class="search-container">
        <el-form :model="searchForm" inline>
          <el-form-item label="燃料包名称：">
            <el-input
              v-model="searchForm.packageName"
              placeholder="请输入燃料包名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="状态：">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择状态"
              clearable
              style="width: 150px"
            >
              <el-option :value="1" label="启用" />
              <el-option :value="0" label="禁用" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              v-if="hasPermission('search')"
              type="primary"
              :loading="searchLoading"
              @click="handleSearch"
            >
              <Icon icon="ep:search" class="mr-5px" />
              搜索
            </el-button>
            <el-button v-if="hasPermission('add')" type="success" @click="handleAdd">
              <Icon icon="ep:plus" class="mr-5px" />
              新增燃料包
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格 -->
      <div style="margin-top: 20px">
        <el-table :data="tableData" v-loading="loading" border>
          <el-table-column prop="packageName" label="燃料包名称" min-width="150" />
          <el-table-column prop="packageType" label="类型" min-width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getTypeTag(row.packageType)">
                {{ getTypeText(row.packageType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="dataAmount" label="流量(MB)" min-width="120" align="right" />
          <el-table-column prop="price" label="价格(元)" min-width="100" align="right" />
          <el-table-column prop="validDays" label="有效期(天)" min-width="100" align="center" />
          <el-table-column prop="status" label="状态" min-width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getStatusTag(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" min-width="160" />
          <el-table-column label="操作" min-width="150" align="center" fixed="right">
            <template #default="{ row }">
              <el-button
                v-if="hasPermission('edit')"
                type="primary"
                size="small"
                @click="handleEdit(row)"
              >
                编辑
              </el-button>
              <el-button
                v-if="hasPermission('delete')"
                type="danger"
                size="small"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div style="margin-top: 20px; text-align: right">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'

// 权限检查函数
const hasPermission = (permission: string): boolean => {
  console.log(`🔍 [燃料包管理权限检查] ${permission}: 允许访问`)
  return true
}

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)

const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

const searchForm = reactive({
  packageName: '',
  status: null as number | null
})

const tableData = ref<any[]>([])

// 方法
const getTypeTag = (type: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    '1': 'primary',
    '2': 'success',
    '3': 'warning'
  }
  return typeMap[type] || 'info'
}

const getTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    '1': '日包',
    '2': '月包',
    '3': '年包'
  }
  return typeMap[type] || '未知'
}

const getStatusTag = (status: number): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  return status === 1 ? 'success' : 'danger'
}

const getStatusText = (status: number) => {
  return status === 1 ? '启用' : '禁用'
}

const handleSearch = () => {
  currentPage.value = 1
  getTableData()
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  getTableData()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  getTableData()
}

const handleAdd = () => {
  ElMessage.info('新增燃料包功能开发中...')
}

const handleEdit = (row: any) => {
  ElMessage.info(`编辑燃料包: ${row.packageName}`)
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除燃料包 "${row.packageName}" 吗？`, '确认删除', {
      type: 'warning'
    })

    ElMessage.success('删除成功')
    getTableData()
  } catch (error) {
    // 用户取消操作
  }
}

// 获取表格数据
const getTableData = async () => {
  try {
    loading.value = true
    // TODO: 实现API调用
    // 模拟数据
    tableData.value = [
      {
        id: 1,
        packageName: '日流量包100MB',
        packageType: '1',
        dataAmount: 100,
        price: 5.0,
        validDays: 1,
        status: 1,
        createTime: '2024-01-01 10:00:00'
      },
      {
        id: 2,
        packageName: '月流量包1GB',
        packageType: '2',
        dataAmount: 1024,
        price: 30.0,
        validDays: 30,
        status: 1,
        createTime: '2024-01-01 11:00:00'
      }
    ]
    total.value = 2
  } catch (error) {
    ElMessage.error('获取燃料包数据失败')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  getTableData()
})
</script>

<style scoped>
.search-container {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}
</style>
