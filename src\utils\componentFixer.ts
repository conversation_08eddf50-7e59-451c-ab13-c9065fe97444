/**
 * 组件修复工具
 * 用于批量修复组件中的常见问题
 */

/**
 * 为组件添加基础的安全防护
 */
export function addBasicSafety(componentScript: string): string {
  // 检查是否已经导入了安全组合式函数
  if (
    componentScript.includes('useBasicSafety') ||
    componentScript.includes('useComponentSafety')
  ) {
    return componentScript
  }

  // 添加导入
  const importRegex = /import.*from.*vue.*\n/
  const match = componentScript.match(importRegex)

  if (match) {
    const importLine = match[0]
    const newImport = `${importLine}import { useBasicSafety } from '@/composables/useBasicSafety'\n`
    componentScript = componentScript.replace(importLine, newImport)
  }

  // 添加安全组合式函数的使用
  const setupRegex = /(const.*=.*ref\(.*\)\n)/
  if (setupRegex.test(componentScript)) {
    componentScript = componentScript.replace(
      setupRegex,
      `// 使用基础安全防护\nconst { isMounted, safeDOMOperation, safeAsyncOperation } = useBasicSafety()\n\n$1`
    )
  }

  return componentScript
}

/**
 * 修复常见的DOM操作问题
 */
export function fixDOMOperations(componentScript: string): string {
  // 包装可能有问题的DOM操作
  const domOperations = [
    'router.push',
    'ElMessage.',
    'document.querySelector',
    'element.focus()',
    'element.blur()'
  ]

  domOperations.forEach((operation) => {
    const regex = new RegExp(`(\\s+)(${operation.replace('.', '\\.')})`, 'g')
    componentScript = componentScript.replace(regex, (match, whitespace, op) => {
      return `${whitespace}safeDOMOperation(() => ${op})`
    })
  })

  return componentScript
}

/**
 * 添加错误边界
 */
export function addErrorBoundary(template: string): string {
  // 检查是否已经有错误边界
  if (template.includes('v-if="!hasError"') || template.includes('error-boundary')) {
    return template
  }

  // 在主要内容周围添加错误边界
  const contentRegex = /(<template>\s*)(.*?)(\s*<\/template>)/s
  const match = template.match(contentRegex)

  if (match) {
    const [, openTag, content, closeTag] = match
    const wrappedContent = `
  <!-- 错误边界 -->
  <div v-if="hasError" class="error-container">
    <el-alert
      title="页面加载出错"
      type="error"
      :closable="false"
      show-icon
    >
      <template #default>
        <p>页面暂时无法正常显示，请刷新页面重试。</p>
        <el-button @click="location.reload()" type="primary" size="small">
          刷新页面
        </el-button>
      </template>
    </el-alert>
  </div>
  
  <!-- 主要内容 -->
  <div v-else>
${content}
  </div>`

    return `${openTag}${wrappedContent}${closeTag}`
  }

  return template
}

/**
 * 修复组件的完整流程
 */
export function fixComponent(componentContent: string): string {
  // 分离模板和脚本
  const templateMatch = componentContent.match(/<template>(.*?)<\/template>/s)
  const scriptMatch = componentContent.match(/<script setup lang="ts">(.*?)<\/script>/s)
  const styleMatch = componentContent.match(/<style.*?>(.*?)<\/style>/s)

  if (!templateMatch || !scriptMatch) {
    console.warn('无法解析组件结构')
    return componentContent
  }

  let template = templateMatch[1]
  let script = scriptMatch[1]
  const style = styleMatch ? styleMatch[0] : ''

  // 应用修复
  template = addErrorBoundary(`<template>${template}</template>`)
  script = addBasicSafety(script)
  script = fixDOMOperations(script)

  // 重新组合
  const fixedComponent = `${template}

<script setup lang="ts">
${script}
</script>

${style}`

  return fixedComponent
}

/**
 * 检查组件是否需要修复
 */
export function needsFix(componentContent: string): boolean {
  const issues = [
    // 缺少安全防护
    !componentContent.includes('useBasicSafety') &&
      !componentContent.includes('useComponentSafety'),
    // 有未保护的DOM操作
    componentContent.includes('router.push') && !componentContent.includes('safeDOMOperation'),
    // 缺少错误边界
    !componentContent.includes('v-if="!hasError"') && !componentContent.includes('error-boundary')
  ]

  return issues.some((issue) => issue)
}
