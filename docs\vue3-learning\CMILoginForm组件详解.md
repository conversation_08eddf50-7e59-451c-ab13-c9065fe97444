# CMILoginForm 组件详解 - Vue 3 学习文档

## 概述

`CMILoginForm.vue` 是一个基于 Vue 3 Composition API 构建的登录表单组件，展示了现代 Vue 3 开发的最佳实践。该组件集成了用户名密码登录、验证码验证、SSO 单点登录等功能。

## 文件结构

```
src/views/Login/components/CMILoginForm.vue
├── <script setup lang="tsx">     # 使用 Composition API 和 TSX
├── <template>                    # 模板部分
└── <style scoped>               # 样式部分
```

## Vue 3 核心特性应用

### 1. Composition API 基础

#### setup 语法糖
```typescript
<script setup lang="tsx">
// 使用 <script setup> 语法糖，自动暴露变量和函数
// 相比 Vue 2 的 Options API，代码更简洁、逻辑更集中
```

**Vue 3 改进点**：
- 无需手动 return 暴露的变量
- 更好的 TypeScript 支持
- 逻辑复用更简单

#### 响应式数据声明
```typescript
// 基本响应式数据
const loading = ref(false)              // ref() 用于基本类型
const showCaptcha = ref(false)
const captchaImage = ref('')
const captchaId = ref('')
const remember = ref(false)

// 复杂对象响应式
const rules = {                         // 表单验证规则对象
  username: [required()],
  password: [required()],
  captcha: [/* ... */]
}
```

**学习要点**：
- `ref()`: 用于基本类型数据（string, number, boolean）
- `reactive()`: 用于对象类型数据
- 在模板中自动解包，在 script 中需要 `.value`

### 2. 生命周期钩子

```typescript
import { onMounted } from 'vue'

onMounted(async () => {
  // 组件挂载后执行的逻辑
  // 1. 检查 ticket 登录
  // 2. 检查 SSO 配置
  // 3. 初始化验证码
})
```

**Vue 3 vs Vue 2**：
- Vue 2: `mounted() { }`
- Vue 3: `onMounted(() => { })`
- 更灵活的组合式使用

### 3. 计算属性和监听器

```typescript
import { computed, watch } from 'vue'

// 计算属性（示例）
const isFormValid = computed(() => {
  return formData.username && formData.password
})

// 监听器（示例）
watch(showCaptcha, (newVal) => {
  if (newVal) {
    addCaptchaField()
  }
})
```

### 4. 组件通信

#### Props 定义
```typescript
// Vue 3 中使用 defineProps 宏
interface Props {
  // 定义 props 类型
}
const props = defineProps<Props>()
```

#### 事件发射
```typescript
// 定义可发射的事件
const emit = defineEmits(['to-register'])

// 发射事件
const toRegister = () => {
  emit('to-register')
}
```

## 技术栈集成详解

### 1. Element Plus 集成

```typescript
import { ElCheckbox, ElLink, ElMessage, ElButton } from 'element-plus'

// 按需导入，减少包体积
// 在模板中直接使用
```

**最佳实践**：
- 按需导入组件
- 使用 TypeScript 类型支持
- 统一的设计语言

### 2. Pinia 状态管理

```typescript
import { useUserStore } from '@/store/modules/user'

const userStore = useUserStore()

// 调用 store 中的方法
await userStore.cmiLogin(formData)
```

**Pinia vs Vuex**：
- 更简洁的 API
- 完整的 TypeScript 支持
- 自动的代码分割

### 3. Vue Router 4

```typescript
import { useRouter } from 'vue-router'

const { push } = useRouter()

// 编程式导航
push({ name: 'home' })
```

### 4. 表单处理

```typescript
import { useForm } from '@/hooks/web/useForm'
import { useValidator } from '@/hooks/web/useValidator'

// 自定义 Hook 的使用
const { register, getElFormExpose, getFormData } = useForm()
const { required } = useValidator()
```

## 业务逻辑实现

### 1. 登录流程

```typescript
const signIn = async () => {
  const formRef = await getElFormExpose()
  await formRef?.validate(async (isValid) => {
    if (isValid) {
      loading.value = true
      try {
        // 1. 获取表单数据
        const formData = await getFormData<LoginData>()
        
        // 2. 添加验证码信息
        if (showCaptcha.value) {
          formData.captchaId = captchaId.value
        }
        
        // 3. 执行登录
        const res = await userStore.cmiLogin(formData)
        
        // 4. 处理登录结果
        // ...
      } catch (error) {
        // 错误处理
      } finally {
        loading.value = false
      }
    }
  })
}
```

### 2. SSO 登录处理

```typescript
// SSO 重定向
const handleSSO = () => {
  console.log('开始 SSO 登录')
  handleSSORedirect()  // 重定向到 CAS 服务器
}

// 页面初始化时检查 ticket
onMounted(async () => {
  try {
    const ticketSuccess = await checkAndHandleTicket(userStore)
    if (ticketSuccess) {
      return // ticket 登录成功，无需显示表单
    }
  } catch (error) {
    console.error('Ticket 登录失败:', error)
  }
  // ... 其他初始化逻辑
})
```

### 3. 验证码处理

```typescript
// 刷新验证码
const refreshCaptcha = async () => {
  try {
    const res = await getVerCode()
    if (res.data) {
      captchaImage.value = (res.data as any).captchaImage
      captchaId.value = (res.data as any).captchaId
    }
  } catch (error) {
    console.error('获取验证码失败:', error)
  }
}

// 动态添加验证码字段
const addCaptchaField = () => {
  const captchaField = {
    field: 'captcha',
    label: '验证码',
    component: 'Input',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请输入验证码'
    }
  }
  
  if (!schema.some(item => item.field === 'captcha')) {
    schema.push(captchaField)
  }
}
```

## TypeScript 最佳实践

### 1. 类型定义

```typescript
import type { LoginData } from '@/api/cmi/auth'

// 使用 type 关键字导入类型
// 编译时会被移除，不影响运行时
```

### 2. 类型断言

```typescript
// 安全的类型断言
const configRes = await getSSOSwitchConfig({})
if (configRes.data) {
  const ssoSwitchConfig = (configRes.data as any).ssoSwitchConfig
}
```

### 3. 函数类型

```typescript
// 异步函数类型
const signIn = async (): Promise<void> => {
  // 函数实现
}

// 事件处理函数
const handleSSO = (): void => {
  handleSSORedirect()
}
```

## 性能优化技巧

### 1. 按需加载

```typescript
// 只导入需要的组件
import { ElCheckbox, ElLink, ElMessage, ElButton } from 'element-plus'
```

### 2. 响应式优化

```typescript
// 使用 ref 而不是 reactive 处理基本类型
const loading = ref(false)  // ✅ 推荐
// const state = reactive({ loading: false })  // ❌ 不推荐
```

### 3. 事件处理优化

```typescript
// 使用箭头函数避免 this 绑定问题
const handleClick = () => {
  // 处理逻辑
}
```

## 学习资源

### 官方文档
- [Vue 3 官方文档](https://cn.vuejs.org/)
- [Composition API 指南](https://cn.vuejs.org/guide/extras/composition-api-faq.html)
- [Element Plus 文档](https://element-plus.org/zh-CN/)
- [Pinia 文档](https://pinia.vuejs.org/zh/)

### 关键概念
1. **Composition API**: Vue 3 的核心特性，提供更灵活的逻辑组合
2. **响应式系统**: ref、reactive、computed、watch 的使用
3. **生命周期**: onMounted、onUnmounted 等钩子函数
4. **组件通信**: props、emit、provide/inject
5. **TypeScript 集成**: 类型安全的开发体验

### 下一步学习建议
1. 深入理解 Composition API 的设计理念
2. 学习自定义 Hook 的编写和复用
3. 掌握 Pinia 状态管理的高级用法
4. 了解 Vue 3 的性能优化技巧
5. 实践 TypeScript 在 Vue 3 中的最佳实践
