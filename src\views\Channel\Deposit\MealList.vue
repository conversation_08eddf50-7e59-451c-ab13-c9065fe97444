<template>
  <!-- 可购套餐列表 -->
  <ContentWrap>
    <!-- 加载状态 -->
    <div v-if="!isComponentMounted" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>

    <!-- 主要内容 -->
    <el-card v-else>
      <div class="search-header">
        <el-button @click="goBack" type="default" style="margin-bottom: 20px">
          <Icon icon="ep:arrow-left" />
          返回
        </el-button>
        <h3>可购套餐列表</h3>
      </div>

      <!-- 套餐列表 -->
      <div style="margin-top: 20px">
        <el-table :data="packageList" v-loading="loading" border>
          <el-table-column prop="packageName" label="套餐名称" min-width="150" />
          <el-table-column prop="packageType" label="套餐类型" min-width="120" align="center">
            <template #default="scope">
              <el-tag :type="getTypeTag(scope.row.packageType)">
                {{ getTypeText(scope.row.packageType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="dataAmount" label="流量(GB)" min-width="100" align="right">
            <template #default="scope">
              {{ (scope.row.dataAmount / 1024).toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="price" label="价格(元)" min-width="100" align="right" />
          <el-table-column prop="validDays" label="有效期(天)" min-width="100" align="center" />
          <el-table-column prop="description" label="描述" min-width="200" />
          <el-table-column label="操作" min-width="120" align="center" fixed="right">
            <template #default="scope">
              <el-button
                v-if="hasPermission('buy')"
                type="success"
                size="small"
                @click="handleBuy(scope.row)"
              >
                购买
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div style="margin-top: 15px; text-align: right">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'
import { useRouter, useRoute } from 'vue-router'
import { useComponentSafety } from '@/composables/useComponentSafety'
import { getAvailablePackages, buyPackage } from '@/api/cmi/channel'

// 权限检查函数
const hasPermission = (permission: string): boolean => {
  return true // 临时返回true，实际应该从权限系统获取
}

const router = useRouter()
const route = useRoute()

// 使用组件安全性管理
const { isComponentMounted, isLoading, safeNavigate, safeLoadData, initComponentSafety } =
  useComponentSafety('可购套餐')

// 响应式数据
const loading = isLoading
const packageList = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 获取套餐类型标签
const getTypeTag = (type: string) => {
  const typeMap = {
    '1': 'primary',
    '2': 'success',
    '3': 'warning',
    '4': 'danger'
  }
  return typeMap[type] || 'info'
}

// 获取套餐类型文本
const getTypeText = (type: string) => {
  const typeMap = {
    '1': '基础套餐',
    '2': '标准套餐',
    '3': '高级套餐',
    '4': '企业套餐'
  }
  return typeMap[type] || '未知'
}

// 获取套餐列表数据
const getPackageData = () => {
  return safeLoadData(async () => {
    try {
      console.log('📊 [可购套餐] 开始获取套餐数据')

      const corpId = route.query.corpId
      if (!corpId) {
        throw new Error('未获取到企业ID')
      }

      const response = await getAvailablePackages({
        corpId,
        page: currentPage.value,
        pageSize: pageSize.value
      })

      console.log('✅ [可购套餐] API响应:', response)

      if (response?.data) {
        packageList.value = response.data.list || []
        total.value = response.data.total || 0

        console.log('✅ [可购套餐] 数据更新完成:', {
          packageList: packageList.value,
          total: total.value
        })
      } else {
        // 使用模拟数据
        packageList.value = [
          {
            id: 1,
            packageName: '基础流量包',
            packageType: '1',
            dataAmount: 1024,
            price: 10.0,
            validDays: 30,
            description: '适合轻度使用的基础流量包'
          },
          {
            id: 2,
            packageName: '标准流量包',
            packageType: '2',
            dataAmount: 5120,
            price: 45.0,
            validDays: 30,
            description: '适合日常使用的标准流量包'
          }
        ]
        total.value = 2
      }

      return true
    } catch (error) {
      console.error('❌ [可购套餐] API调用失败:', error)
      ElMessage.error('获取套餐数据失败')
      return false
    }
  }, '获取套餐数据失败')
}

// 购买套餐
const handleBuy = async (packageInfo: any) => {
  try {
    const result = await ElMessageBox.confirm(
      `确定要购买套餐"${packageInfo.packageName}"吗？价格：${packageInfo.price}元`,
      '确认购买',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    if (result === 'confirm') {
      console.log('🛒 [可购套餐] 开始购买套餐:', packageInfo)

      const corpId = route.query.corpId
      const response = await buyPackage({
        corpId,
        packageId: packageInfo.id,
        packageName: packageInfo.packageName,
        price: packageInfo.price
      })

      if (response?.success) {
        ElMessage.success('套餐购买成功')
        // 刷新数据
        await getPackageData()
      } else {
        ElMessage.error(response?.message || '套餐购买失败')
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('❌ [可购套餐] 购买失败:', error)
      ElMessage.error('套餐购买失败')
    }
  }
}

// 返回上一页
const goBack = () => {
  safeNavigate(
    router,
    {
      path: '/newcmi/channel/deposit'
    },
    '返回失败'
  )
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  getPackageData()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  getPackageData()
}

// 在组件挂载后自动加载数据
initComponentSafety(async () => {
  await getPackageData()
})
</script>

<style scoped>
.loading-container {
  padding: 20px;
}

.search-header {
  display: flex;
  align-items: center;
  gap: 10px;
}

.search-header h3 {
  margin: 0;
  color: #409eff;
}
</style>
