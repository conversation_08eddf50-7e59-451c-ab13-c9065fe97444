/**
 * 全局错误处理器
 * 专门处理DOM操作相关的错误
 */

// 错误类型定义
interface DOMError extends Error {
  target?: any
  currentTarget?: any
}

/**
 * 检查是否为DOM相关错误
 */
export function isDOMError(error: Error): boolean {
  const domErrorMessages = [
    "Cannot read properties of null (reading 'parentNode')",
    "Cannot read properties of null (reading 'subTree')",
    "Cannot read properties of undefined (reading 'parentNode')",
    "Cannot read properties of undefined (reading 'subTree')",
    'Cannot access before initialization',
    'Node was not found'
  ]

  return domErrorMessages.some((msg) => error.message.includes(msg))
}

/**
 * DOM错误处理器
 */
export function handleDOMError(error: DOMError, context?: string): void {
  console.warn(`🚫 [DOM错误处理] ${context || '未知上下文'}:`, error.message)

  // 不向用户显示DOM错误，因为这些通常是内部错误
  // 只在开发环境中显示详细信息
  if (import.meta.env.DEV) {
    console.error('DOM错误详情:', error)
  }
}

/**
 * 安全的DOM元素访问
 */
export function safeGetElement(selector: string): Element | null {
  try {
    return document.querySelector(selector)
  } catch (error) {
    console.warn(`🚫 [DOM访问] 无法找到元素: ${selector}`)
    return null
  }
}

/**
 * 安全的DOM元素操作
 */
export function safeElementOperation(
  element: Element | null,
  operation: (el: Element) => void,
  errorMsg = 'DOM操作失败'
): boolean {
  if (!element) {
    console.warn(`🚫 [DOM操作] 元素不存在: ${errorMsg}`)
    return false
  }

  try {
    // 检查元素是否仍在DOM中
    if (!document.contains(element)) {
      console.warn(`🚫 [DOM操作] 元素已从DOM中移除: ${errorMsg}`)
      return false
    }

    operation(element)
    return true
  } catch (error) {
    console.warn(`🚫 [DOM操作] ${errorMsg}:`, error)
    return false
  }
}

/**
 * 全局错误处理器设置
 */
export function setupGlobalErrorHandler(): void {
  // 处理未捕获的错误
  window.addEventListener('error', (event) => {
    if (isDOMError(event.error)) {
      handleDOMError(event.error, '全局错误捕获')
      event.preventDefault() // 阻止默认的错误处理
    }
  })

  // 处理未捕获的Promise拒绝
  window.addEventListener('unhandledrejection', (event) => {
    if (event.reason && isDOMError(event.reason)) {
      handleDOMError(event.reason, 'Promise拒绝')
      event.preventDefault() // 阻止默认的错误处理
    }
  })

  console.log('✅ 全局DOM错误处理器已设置')
}
