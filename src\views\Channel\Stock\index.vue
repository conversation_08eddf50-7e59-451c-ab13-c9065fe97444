<template>
  <ContentWrap title="库存管理" message="管理渠道库存信息和任务状态">
    <!-- 搜索表单 -->
    <div class="search-form mb-20px">
      <ElForm :model="searchForm" inline>
        <ElFormItem label="订单号">
          <ElInput
            v-model="searchForm.taskName"
            placeholder="请输入订单号"
            clearable
            style="width: 200px"
          />
        </ElFormItem>
        <ElFormItem label="时间段">
          <ElDatePicker
            v-model="searchForm.timeSlot"
            type="daterange"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />
        </ElFormItem>
        <ElFormItem>
          <ElButton
            v-if="checkPermission(['search'])"
            type="primary"
            :loading="searchLoading"
            @click="handleSearch"
          >
            <Icon icon="ep:search" class="mr-5px" />
            搜索
          </ElButton>
          <ElButton v-if="checkPermission(['showiccid'])" type="warning" @click="showICCID">
            显示ICCID
          </ElButton>
        </ElFormItem>
      </ElForm>
    </div>

    <!-- 表格 -->
    <div class="table-container mb-20px">
      <ElTable
        :data="tableData"
        v-loading="loading"
        border
        empty-text="暂无数据"
        :default-sort="{ prop: 'createTime', order: 'descending' }"
        style="width: 100%"
      >
        <ElTableColumn prop="taskName" label="任务名称" min-width="150">
          <template #default="{ row }">
            <strong>{{ row?.taskName || '-' }}</strong>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="taskNum" label="任务编号" min-width="150">
          <template #default="{ row }">
            <strong>{{ row?.taskNum || '-' }}</strong>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="totalCount" label="总数量" min-width="100" align="center">
          <template #default="{ row }">
            {{ row?.totalCount || 0 }}
          </template>
        </ElTableColumn>
        <ElTableColumn prop="successCount" label="成功数量" min-width="100" align="center">
          <template #default="{ row }">
            {{ row?.successCount || 0 }}
          </template>
        </ElTableColumn>
        <ElTableColumn prop="failCount" label="失败数量" min-width="100" align="center">
          <template #default="{ row }">
            {{ row?.failCount || 0 }}
          </template>
        </ElTableColumn>
        <ElTableColumn prop="status" label="状态" min-width="100" align="center">
          <template #default="{ row }">
            <ElTag :type="getStatusType(row?.status || '0')" v-if="row">
              {{ getStatusText(row?.status || '0') }}
            </ElTag>
            <span v-else>-</span>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="createTime" label="创建时间" min-width="160">
          <template #default="{ row }">
            <strong>{{ row?.createTime || '-' }}</strong>
          </template>
        </ElTableColumn>
        <ElTableColumn label="操作" min-width="120" align="center" fixed="right">
          <template #default="{ row }">
            <ElButton
              v-if="checkPermission(['view']) && row && row.id"
              type="warning"
              size="small"
              @click="viewDetails(row)"
            >
              详情
            </ElButton>
            <span v-else-if="!row">-</span>
          </template>
        </ElTableColumn>
      </ElTable>
    </div>

    <!-- 分页 -->
    <div class="pagination-container text-right">
      <ElPagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="handlePageChange"
        @size-change="handleSizeChange"
      />
    </div>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import {
  ElMessage,
  ElForm,
  ElFormItem,
  ElInput,
  ElDatePicker,
  ElButton,
  ElTable,
  ElTableColumn,
  ElTag,
  ElPagination
} from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'
import { useRouter } from 'vue-router'

defineOptions({
  name: 'ChannelStockManagement'
})

const { push } = useRouter()

// 权限检查函数
const checkPermission = (permissions: string[]): boolean => {
  // 临时返回true，实际应该使用用户权限进行检查
  return true
}

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)

const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

const searchForm = reactive({
  taskName: '',
  timeSlot: [] as string[]
})

const tableData = ref<any[]>([])

// 方法
const getStatusType = (status: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const statusMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    '1': 'success',
    '2': 'warning',
    '3': 'danger',
    '0': 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '1': '已完成',
    '2': '进行中',
    '3': '失败',
    '0': '待处理'
  }
  return statusMap[status] || '未知'
}

// 搜索处理
const handleSearch = () => {
  console.log('🔍 [库存管理] 执行搜索操作')
  currentPage.value = 1
  getTableData()
}

// 分页处理
const handlePageChange = (page: number) => {
  console.log('📄 [库存管理] 切换到第', page, '页')
  currentPage.value = page
  getTableData()
}

const handleSizeChange = (size: number) => {
  console.log('📏 [库存管理] 页面大小变更为', size)
  pageSize.value = size
  currentPage.value = 1
  getTableData()
}

// 查看详情
const viewDetails = (row: any) => {
  if (!row || !row.id) {
    console.warn('🚫 [库存管理] 无效的任务数据')
    return
  }

  push({
    path: '/newcmi/channel/stock/card-list',
    query: {
      taskId: row.id,
      taskName: row.taskName
    }
  })
}

// 显示ICCID
const showICCID = () => {
  try {
    console.log('🔗 [库存管理] 跳转到ICCID显示页面')
    push({
      path: '/newcmi/channel/stock/show-iccid'
    })
  } catch (error) {
    console.error('❌ [库存管理] 路由跳转失败:', error)
    ElMessage.error('页面跳转失败')
  }
}

// 获取表格数据
const getTableData = async () => {
  try {
    loading.value = true
    console.log('📊 [库存管理] 开始加载表格数据')

    // 使用 nextTick 确保DOM已更新
    await nextTick()

    // TODO: 实现API调用
    // 模拟异步数据加载
    await new Promise((resolve) => setTimeout(resolve, 100))

    // 模拟数据
    const mockData = [
      {
        id: 1,
        taskName: '库存补充任务001',
        taskNum: 'TASK20240101001',
        totalCount: 1000,
        successCount: 950,
        failCount: 50,
        status: '1',
        createTime: '2024-01-01 10:00:00'
      },
      {
        id: 2,
        taskName: '库存补充任务002',
        taskNum: 'TASK20240101002',
        totalCount: 500,
        successCount: 300,
        failCount: 0,
        status: '2',
        createTime: '2024-01-01 11:00:00'
      }
    ]

    // 更新数据
    tableData.value = mockData
    total.value = mockData.length
    console.log('✅ [库存管理] 表格数据加载完成')
  } catch (error) {
    console.error('❌ [库存管理] 数据加载失败:', error)
    ElMessage.error('获取库存数据失败')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(async () => {
  console.log('🎉 [库存管理] 组件开始挂载')

  try {
    // 等待DOM完全渲染
    await nextTick()

    console.log('✅ [库存管理] 组件挂载完成，开始加载数据')

    // 延迟一小段时间确保所有DOM元素都已准备好
    setTimeout(() => {
      getTableData()
    }, 50)
  } catch (error) {
    console.error('❌ [库存管理] 组件挂载失败:', error)
  }
})

onUnmounted(() => {
  console.log('🔄 [库存管理] 组件开始卸载')

  // 清理加载状态
  loading.value = false
  searchLoading.value = false

  // 清理数据
  tableData.value = []

  console.log('✅ [库存管理] 组件卸载完成')
})
</script>

<style scoped>
/* 搜索表单样式 */
.search-form {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.search-form :deep(.el-form-item) {
  margin-bottom: 0;
}

/* 表格容器 */
.table-container {
  margin-bottom: 20px;
}

/* 分页容器 */
.pagination-container {
  text-align: right;
  margin-top: 20px;
}

/* 表格行悬停效果 */
:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

/* 确保表格在加载时不会闪烁 */
:deep(.el-table) {
  min-height: 200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-form :deep(.el-form--inline .el-form-item) {
    display: block;
    margin-bottom: 15px;
  }

  .pagination-container {
    text-align: center;
  }
}
</style>
