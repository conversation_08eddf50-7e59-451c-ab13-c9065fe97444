import { AxiosResponse, InternalAxiosRequestConfig } from './types'
import { ElMessage } from 'element-plus'
import qs from 'qs'
import { SUCCESS_CODE, TRANSFORM_REQUEST_DATA } from '@/constants'
import { useUserStoreWithOut } from '@/store/modules/user'
import { objToFormData } from '@/utils'
import router from '@/router'

const defaultRequestInterceptors = (config: InternalAxiosRequestConfig) => {
  const userStore = useUserStoreWithOut()

  // CMI 项目特殊处理：添加 token 和用户名到请求头
  if (userStore.getToken) {
    const token = userStore.getToken
    const userName = userStore.getUserName || ''

    // 如果 params 不是 false，则添加 Authorization header
    if (config.params !== false) {
      config.headers.Authorization = `bearer ${token}`
    } else {
      // 清空下发短信接口的token（CMI项目特殊需求）
      userStore.resetToken()
    }

    // 添加用户名到请求头（CMI项目需求）
    if (userName) {
      config.headers.userName = encodeURIComponent(userName)
    }
  }

  if (
    config.method === 'post' &&
    config.headers['Content-Type'] === 'application/x-www-form-urlencoded'
  ) {
    config.data = qs.stringify(config.data)
  } else if (
    TRANSFORM_REQUEST_DATA &&
    config.method === 'post' &&
    config.headers['Content-Type'] === 'multipart/form-data' &&
    !(config.data instanceof FormData)
  ) {
    config.data = objToFormData(config.data)
  }
  if (config.method === 'get' && config.params) {
    let url = config.url as string
    url += '?'
    const keys = Object.keys(config.params)
    for (const key of keys) {
      if (config.params[key] !== void 0 && config.params[key] !== null) {
        url += `${key}=${encodeURIComponent(config.params[key])}&`
      }
    }
    url = url.substring(0, url.length - 1)
    config.params = {}
    config.url = url
  }
  return config
}

// CMI 项目错误码处理函数
const handleCMIErrorCode = (result: any) => {
  const userStore = useUserStoreWithOut()

  if (result.code === '4000') {
    router.push({ name: 'authenError' })
    return
  }

  if (result.code === '4001' || result.code === '4002') {
    // token校验失败，跳转到登录页面
    userStore.logout()
    router.push({ name: 'login' })
    setTimeout(() => {
      ElMessage.error('Token已过期，请重新登录')
    }, 1000)
  } else {
    // 全局错误提示
    ElMessage.error(result.msg || result.message || '请求失败')
  }
}

const defaultResponseInterceptors = (response: AxiosResponse) => {
  if (response?.config?.responseType === 'blob') {
    // 处理文件下载
    if (response.headers['content-type'] === 'application/json;charset=UTF-8') {
      // 处理文件下载失败，返回错误信息的情况
      const reader = new FileReader()
      reader.onload = (e) => {
        if (e.target?.readyState === FileReader.DONE) {
          const result = JSON.parse(e.target.result as string)
          if (result.code !== '0000') {
            handleCMIErrorCode(result)
          }
        }
      }
      reader.readAsText(response.data)
      return
    }
    return response
  }

  // CMI 项目使用 '0000' 作为成功码
  if (response.data.code === '0000') {
    return response.data
  } else if (response.data.code === SUCCESS_CODE) {
    // 兼容原框架的成功码
    return response.data
  } else {
    // 处理 CMI 项目的错误码
    handleCMIErrorCode(response.data)
    return Promise.reject(response.data)
  }
}

export { defaultResponseInterceptors, defaultRequestInterceptors }
