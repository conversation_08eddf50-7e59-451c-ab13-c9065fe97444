# 渠道自服务模块

本目录包含从 cmi-web 项目迁移的渠道自服务模块的所有组件。

## 模块结构

### 账户管理相关
- `Deposit/` - 充值管理
  - `index.vue` - 充值管理主页面
  - `MarketingAccount.vue` - 营销账户
  - `MealList.vue` - 套餐列表
  - `StreamList.vue` - 流量列表
  - `OfflinePayment.vue` - 线下支付

### 库存管理相关
- `Stock/` - 库存管理
  - `index.vue` - 库存管理主页面
  - `CardList.vue` - 卡片列表
  - `ShowICCID.vue` - ICCID显示

### 业务管理相关
- `AQCode/` - AQ码管理
- `FuelPack/` - 燃料包管理
- `Package/` - 套餐管理
- `BuyMeal/` - 购买套餐

### 订单管理相关
- `Order/` - 订单管理
  - `index.vue` - 订单列表
  - `Bill.vue` - 账单管理
- `PaymentOrder/` - 支付订单管理

### 服务支持相关
- `Support/` - 服务支持
  - `index.vue` - 服务支持主页面
  - `DetailsList.vue` - 详情列表
  - `UseList.vue` - 使用列表
  - `LocationPackage.vue` - 位置套餐
  - `PurchasedPackage.vue` - 已购套餐

### 地址管理
- `Address/` - 地址管理

### 流量池管理
- `FlowPool/` - 流量池管理
  - `CardList/` - 卡片列表
  - `FlowList/` - 流量列表
  - `UseRecord/` - 使用记录

### 渠道商管理
- `SubChannelProvider/` - 子渠道商管理
- `WhiteCardOrders/` - 白卡订单管理

### 资源合作
- `ResourceCooperation/` - 资源合作
  - `index.vue` - 资源合作主页面
  - `BillingStatistics/` - 计费统计
  - `CallOrderDetails/` - 呼叫订单详情
  - `ViewResources/` - 查看资源

### 账单查询
- `ChannelBillingQuery/` - 渠道账单查询

## 迁移状态

### ✅ 已完成的迁移工作

#### 1. 目录结构和基础配置
- [x] 创建了完整的目录结构
- [x] 更新了路由配置 (src/types/cmi-menu.ts)
- [x] 添加了渠道自服务模块路由映射

#### 2. 核心组件迁移 (已完成 6/25)
- [x] **充值管理模块 (Deposit)**
  - [x] 主页面 (src/views/CMI/Channel/Deposit/index.vue)
  - [x] 营销账户详情 (src/views/CMI/Channel/Deposit/MarketingAccount.vue)
- [x] **库存管理模块 (Stock)**
  - [x] 主页面 (src/views/CMI/Channel/Stock/index.vue)
- [x] **订单管理模块 (Order)**
  - [x] 主页面 (src/views/CMI/Channel/Order/index.vue)
- [x] **流量池管理模块 (FlowPool)**
  - [x] 主页面 (src/views/CMI/Channel/FlowPool/index.vue)
- [x] **AQ码管理模块 (AQCode)**
  - [x] 主页面 (src/views/CMI/Channel/AQCode/index.vue)

#### 3. 技术栈适配
- [x] Vue 2 → Vue 3 语法转换
- [x] iView → Element Plus 组件库适配
- [x] Options API → Composition API 重构
- [x] TypeScript 类型定义

### 🚧 待完成的迁移工作 (剩余 19/25)

#### 账户管理相关 (剩余 3 个)
- [ ] 套餐列表 (MealList.vue)
- [ ] 流量列表 (StreamList.vue)
- [ ] 线下支付 (OfflinePayment.vue)

#### 库存管理相关 (剩余 1 个)
- [ ] 卡片列表 (CardList.vue)
- [ ] ICCID显示 (ShowICCID.vue)

#### 业务管理相关 (剩余 3 个)
- [ ] 燃料包管理 (FuelPack/)
- [ ] 套餐管理 (Package/)
- [ ] 购买套餐 (BuyMeal/)

#### 订单管理相关 (剩余 1 个)
- [ ] 账单管理 (Bill.vue)

#### 服务支持相关 (剩余 4 个)
- [ ] 服务支持主页面 (Support/index.vue)
- [ ] 详情列表 (DetailsList.vue)
- [ ] 使用列表 (UseList.vue)
- [ ] 位置套餐 (LocationPackage.vue)
- [ ] 已购套餐 (PurchasedPackage.vue)

#### 地址管理 (剩余 1 个)
- [ ] 地址管理 (Address/)

#### 流量池管理子模块 (剩余 3 个)
- [ ] 卡片列表 (FlowPool/CardList/)
- [ ] 流量列表 (FlowPool/FlowList/)
- [ ] 使用记录 (FlowPool/UseRecord/)

#### 渠道商管理 (剩余 2 个)
- [ ] 子渠道商管理 (SubChannelProvider/)
- [ ] 白卡订单管理 (WhiteCardOrders/)

#### 资源合作 (剩余 4 个)
- [ ] 资源合作主页面 (ResourceCooperation/index.vue)
- [ ] 计费统计 (BillingStatistics/)
- [ ] 呼叫订单详情 (CallOrderDetails/)
- [ ] 查看资源 (ViewResources/)

#### 账单查询 (剩余 1 个)
- [ ] 渠道账单查询 (ChannelBillingQuery/)

### 🔧 技术适配工作

#### API接口适配
- [ ] 统一API调用方式
- [ ] 错误处理机制
- [ ] 权限验证逻辑

#### 样式和主题
- [ ] Element Plus 主题适配
- [ ] 响应式布局优化
- [ ] 图标库统一

#### 功能测试
- [ ] 单元测试编写
- [ ] 集成测试验证
- [ ] 用户体验测试

### 📊 迁移进度统计
- **总模块数**: 25 个
- **已完成**: 6 个 (24%)
- **待完成**: 19 个 (76%)
- **预计完成时间**: 需要额外 2-3 天
